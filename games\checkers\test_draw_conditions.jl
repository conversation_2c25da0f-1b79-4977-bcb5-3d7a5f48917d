# Test draw conditions for checkers game

include("Types.jl")
include("Moves.jl")
include("Rules.jl")
include("Vectorization.jl")

# Mock the GI module for testing
module GI
    abstract type AbstractGameSpec end
    abstract type AbstractGameEnv end

    function init end
    function game_terminated end
    function white_reward end
    function white_playing end
end

include("game.jl")

function test_forty_move_rule()
    println("Testing forty-move rule...")
    
    # Initialize game
    spec = CheckersSpec()
    env = GI.init(spec)
    
    # Manually set halfmove_clock to test the rule
    env.halfmove_clock = 79
    
    # Check that it's not a draw yet
    @test !is_forty_move_rule(env)
    @test !is_game_over(env)
    
    # Increment to 80 (forty moves)
    env.halfmove_clock = 80
    
    # Check that it's now a draw
    @test is_forty_move_rule(env)
    @test is_game_over(env)
    
    # Check that determine_winner returns draw
    outcome = determine_winner(env)
    @test outcome == Int8(0)  # Draw
    
    println("✓ Forty-move rule test passed!")
    return true
end

function test_threefold_repetition()
    println("Testing threefold repetition...")
    
    # Initialize game
    spec = CheckersSpec()
    env = GI.init(spec)
    
    # Create a position hash
    test_hash = hash((env.board, env.side_to_move))
    
    # Add the same hash twice (not yet a draw)
    push!(env.position_history, test_hash)
    push!(env.position_history, test_hash)
    
    @test !is_threefold_repetition(env)
    @test !is_game_over(env)
    
    # Add the same hash a third time (now it's a draw)
    push!(env.position_history, test_hash)
    
    @test is_threefold_repetition(env)
    @test is_game_over(env)
    
    # Check that determine_winner returns draw
    outcome = determine_winner(env)
    @test outcome == Int8(0)  # Draw
    
    println("✓ Threefold repetition test passed!")
    return true
end

function test_halfmove_clock_updates()
    println("Testing halfmove clock updates...")
    
    # Initialize game
    spec = CheckersSpec()
    env = GI.init(spec)
    
    # Get a legal move
    legal_moves = generate_all_moves(env.board, env.side_to_move)
    if isempty(legal_moves)
        println("No legal moves available for testing")
        return false
    end
    
    # Find a non-capture move (man move)
    non_capture_move = nothing
    for move in legal_moves
        if isempty(move.captures)
            non_capture_move = move
            break
        end
    end
    
    if non_capture_move !== nothing
        initial_clock = env.halfmove_clock
        
        # Apply the move
        apply!(env, non_capture_move)
        
        # For a man move, the clock should reset to 0
        piece = env.board[non_capture_move.to]  # The piece after the move
        if is_man(piece) || !isempty(non_capture_move.captures)
            @test env.halfmove_clock == 0
        else
            # For king-only moves without captures, it should increment
            @test env.halfmove_clock == initial_clock + 1
        end
        
        # Test undo
        undo!(env)
        @test env.halfmove_clock == initial_clock
        
        println("✓ Halfmove clock update test passed!")
        return true
    else
        println("No suitable moves found for testing halfmove clock")
        return false
    end
end

function test_position_history_tracking()
    println("Testing position history tracking...")
    
    # Initialize game
    spec = CheckersSpec()
    env = GI.init(spec)
    
    initial_history_length = length(env.position_history)
    
    # Get a legal move
    legal_moves = generate_all_moves(env.board, env.side_to_move)
    if isempty(legal_moves)
        println("No legal moves available for testing")
        return false
    end
    
    move = legal_moves[1]
    
    # Apply the move
    apply!(env, move)
    
    # Position history should have one more entry
    @test length(env.position_history) == initial_history_length + 1
    
    # Undo the move
    undo!(env)
    
    # Position history should be back to original length
    @test length(env.position_history) == initial_history_length
    
    println("✓ Position history tracking test passed!")
    return true
end

function run_all_tests()
    println("Running draw condition tests...")
    
    tests = [
        test_forty_move_rule,
        test_threefold_repetition,
        test_halfmove_clock_updates,
        test_position_history_tracking
    ]
    
    passed = 0
    total = length(tests)
    
    for test_func in tests
        try
            if test_func()
                passed += 1
            end
        catch e
            println("Test $(test_func) failed with error: $e")
        end
    end
    
    println("\nTest Results: $passed/$total tests passed")
    
    if passed == total
        println("🎉 All draw condition tests passed!")
        return true
    else
        println("❌ Some tests failed")
        return false
    end
end

# Run tests if this file is executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    run_all_tests()
end
