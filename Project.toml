name = "<PERSON><PERSON><PERSON>"
uuid = "8ed9eb0b-7496-408d-8c8b-2119aeea02cd"
authors = ["<PERSON> <jonathan.la<PERSON><EMAIL>>"]
version = "0.5.4"

[deps]
ArgParse = "c7e460c6-2fb9-53a9-8c5b-16f535851c63"
CUDA = "052768ef-5323-5732-b1bb-66c8b64840ba"
Colors = "5ae59095-9a9b-59fe-a467-6f913c188581"
CommonRLInterface = "d842c3ba-07a1-494f-bbec-f5741b0a3e98"
Crayons = "a8cc5b0e-0ffa-5ad4-8c14-923d3ee1735f"
DataStructures = "864edb3b-99cc-5e75-8d2d-829cb0a9cfe8"
Distributed = "8ba89e20-285c-5b6f-9357-94700520ee1b"
Distributions = "31c24e10-a181-5473-b8eb-7969acd0382f"
Documenter = "e30172f5-a6a5-5a46-863b-614d45cd2de4"
ExprTools = "e2ba6199-217a-4e67-a87a-7c52f15ade04"
Flux = "587475ba-b771-5e3f-ad9e-33799f191a9c"
Formatting = "59287772-0a20-5a39-b81b-1366585eb4c0"
JSON3 = "0f8b85d8-7281-11e9-16c2-39a750bddbf1"
Knet = "1902f260-5fb4-5aff-8c31-6271790ab950"
LinearAlgebra = "37e2e46d-f89d-539d-b4ee-838fcccc9c8e"
LoggingExtras = "e6f89c97-d47a-5376-807f-9c37f3926c36"
Plots = "91a5bcdd-55d7-5caf-9e0b-520d859cae80"
ProgressMeter = "92933f4c-e287-5a05-a399-4b506db050ca"
Random = "9a3f8284-a2c9-5f02-9a11-845980a1fd5c"
Requires = "ae029012-a4dd-5104-9daa-d747884805df"
Serialization = "9e88b42a-f829-5b0c-bbe9-9e923198166b"
Setfield = "efcf1570-3423-57d1-acb7-fd33fddbac46"
StaticArrays = "90137ffa-7385-5640-81b9-e52037218182"
Statistics = "10745b16-79ce-11e8-11f9-7d13ad32a3b2"
ThreadPools = "b189fb0b-2eb5-4ed4-bc0c-d34c51242431"
Zygote = "e88e6eb3-aa80-5325-afca-941959d7151f"

[compat]
ArgParse = "1.1.4"
CUDA = "3"
Colors = "0.12.7"
CommonRLInterface = "0.3.1"
Crayons = "4.0.4"
DataStructures = "0.18.9"
Distributions = "0.24.18, 0.25"
Documenter = "0.26.3, 0.27"
ExprTools = "= 0.1.3, 0.1"
Flux = "0.12.2, 0.13"
Formatting = "0.4.2"
JSON3 = "1.8.1"
Knet = "1.4.8"
LoggingExtras = "0.4.7, 1"
Plots = "1.12.0"
ProgressMeter = "1.5.0"
Requires = "1.1.3"
Setfield = "0.7.0, 0.8, 1"
StaticArrays = "1.1.1"
ThreadPools = "2.0.0"
Zygote = "0.6.10"
julia = "1.6"

[extras]
Test = "8dfed614-e22c-5e08-85e1-65c5234f0b40"

[targets]
test = ["Test"]
